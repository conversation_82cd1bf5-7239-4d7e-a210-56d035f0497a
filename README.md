# Financial Content Creation Application

A comprehensive Python application for managing financial data with export capabilities to Excel and Google Sheets.

## Features

### Core Functionality
- **Financial Data Management**: Track income, expenses, investments, and budgets
- **Data Validation**: Built-in validation and error handling for all financial records
- **Financial Calculations**: Automatic calculation of totals, percentages, growth rates, and financial metrics
- **Category Management**: Organize transactions by categories and tags

### Export Capabilities
- **Excel Export**: Export to .xlsx format with professional formatting, formulas, and multiple worksheets
- **Google Sheets Integration**: Optional cloud-based export with sharing capabilities
- **Dashboard Generation**: Create HTML dashboards with visual summaries and analytics

### User Interface
- **Command Line Interface**: Easy-to-use CLI for data entry and management
- **Data Persistence**: Automatic saving and loading of financial data
- **Summary Analytics**: Comprehensive financial analysis and reporting

## Installation

### Prerequisites
- Python 3.8 or higher
- pip package manager

### Basic Installation
```bash
# Clone or download the application
cd finance-tracker

# Install required dependencies
pip install -r requirements.txt
```

### Optional: Google Sheets Integration
For Google Sheets export functionality:

1. Install additional dependencies:
```bash
pip install gspread google-auth
```

2. Set up Google Service Account:
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one
   - Enable Google Sheets API and Google Drive API
   - Create a service account and download the JSON credentials
   - Save the credentials as `config/google_credentials.json`

## Quick Start

### Running the Application
```bash
python main.py
```

### Basic Usage
1. **Add Financial Records**: Use the menu options to add income, expenses, investments, and budgets
2. **View Data**: Review your records with filtering options
3. **Generate Reports**: View summary analytics and financial insights
4. **Export Data**: Export to Excel or Google Sheets for external use

### Example Workflow
```
1. Start the application: python main.py
2. Choose "1. Add Income" and enter your salary
3. Choose "2. Add Expense" and enter your monthly expenses
4. Choose "6. View Summary & Analysis" to see your financial overview
5. Choose "7. Export Data" to create an Excel file
```

## Project Structure

```
finance-tracker/
├── main.py                          # Application entry point
├── requirements.txt                 # Python dependencies
├── README.md                       # This file
├── src/                            # Source code
│   ├── models/                     # Data models
│   │   ├── base.py                 # Base financial record class
│   │   └── financial_records.py    # Specific record types
│   ├── ui/                         # User interfaces
│   │   └── cli_interface.py        # Command line interface
│   ├── exporters/                  # Export functionality
│   │   ├── excel_exporter.py       # Excel export
│   │   └── google_sheets_exporter.py # Google Sheets export
│   └── utils/                      # Utilities
│       ├── calculations.py         # Financial calculations
│       ├── dashboard_generator.py  # Dashboard and reports
│       └── logger.py              # Logging utilities
├── data/                           # Data storage
├── exports/                        # Exported files
├── logs/                          # Application logs
└── config/                        # Configuration files
```

## Financial Record Types

### Income Records
- Amount, description, source
- Recurring income tracking
- Category classification

### Expense Records
- Amount, description, vendor
- Essential vs. non-essential classification
- Payment method tracking

### Investment Records
- Amount, investment type, symbol
- Share quantity and price tracking
- Portfolio categorization

### Budget Records
- Budget amount and period
- Spending tracking and utilization
- Over-budget alerts

## Export Features

### Excel Export
- **Multiple Worksheets**: Separate sheets for each record type
- **Summary Dashboard**: Overview with key metrics and charts
- **Professional Formatting**: Headers, borders, and number formatting
- **Formulas**: Automatic calculations and totals
- **Date Stamping**: Timestamped exports for version control

### Google Sheets Export
- **Cloud-based Storage**: Access from anywhere
- **Real-time Collaboration**: Share with accountants or family
- **Automatic Formatting**: Professional appearance
- **API Integration**: Secure authentication

## Financial Analytics

### Summary Statistics
- Total income, expenses, and investments
- Net income and savings rate
- Category breakdowns and percentages
- Budget utilization analysis

### Trend Analysis
- Month-over-month comparisons
- Year-over-year growth rates
- Recent transaction patterns
- Financial goal progress

### Alerts and Recommendations
- Budget overage warnings
- Low savings rate alerts
- Investment allocation suggestions
- Expense optimization tips

## Configuration

### Application Settings
- Data file location: `data/financial_data.json`
- Log file location: `logs/`
- Export directory: `exports/`

### Google Sheets Setup
1. Create `config/google_credentials.json` with service account credentials
2. Share target spreadsheets with the service account email
3. Use the Google Sheets export option in the application

## Development

### Running Tests
```bash
pytest tests/
```

### Code Formatting
```bash
black src/
flake8 src/
```

### Adding New Features
1. Create new modules in appropriate `src/` subdirectories
2. Follow the existing class structure and patterns
3. Add comprehensive error handling and logging
4. Update tests and documentation

## Troubleshooting

### Common Issues

**Import Errors**
- Ensure all dependencies are installed: `pip install -r requirements.txt`
- Check Python version compatibility (3.8+)

**Google Sheets Authentication**
- Verify credentials file exists and is valid JSON
- Ensure APIs are enabled in Google Cloud Console
- Check service account permissions

**Data Loading Issues**
- Check file permissions in `data/` directory
- Verify JSON file format is not corrupted
- Review log files for detailed error messages

### Getting Help
- Check the log files in `logs/` for detailed error information
- Ensure all required directories exist (data/, exports/, logs/, config/)
- Verify file permissions for read/write access

## License

This project is provided as-is for educational and personal use.

## Contributing

Feel free to submit issues, feature requests, or improvements to enhance the application's functionality.
