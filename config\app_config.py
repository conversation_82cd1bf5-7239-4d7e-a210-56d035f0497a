"""
Application configuration settings.
"""

import os
from pathlib import Path

class AppConfig:
    """Application configuration class."""
    
    # Application info
    APP_NAME = "Financial Content Creation Application"
    VERSION = "1.0.0"
    
    # Directories
    BASE_DIR = Path(__file__).parent.parent
    DATA_DIR = BASE_DIR / "data"
    EXPORTS_DIR = BASE_DIR / "exports"
    LOGS_DIR = BASE_DIR / "logs"
    CONFIG_DIR = BASE_DIR / "config"
    
    # Data files
    DATA_FILE = DATA_DIR / "financial_data.json"
    BACKUP_DIR = DATA_DIR / "backups"
    
    # Export settings
    EXCEL_TEMPLATE_DIR = CONFIG_DIR / "excel_templates"
    DEFAULT_EXPORT_FORMAT = "xlsx"
    
    # Google Sheets settings
    GOOGLE_CREDENTIALS_FILE = CONFIG_DIR / "google_credentials.json"
    GOOGLE_SCOPES = [
        'https://www.googleapis.com/auth/spreadsheets',
        'https://www.googleapis.com/auth/drive'
    ]
    
    # Logging settings
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_MAX_BYTES = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT = 5
    
    # Financial settings
    DEFAULT_CURRENCY = "USD"
    CURRENCY_SYMBOL = "$"
    DECIMAL_PLACES = 2
    
    # Budget alert thresholds
    BUDGET_WARNING_THRESHOLD = 90  # Percentage
    LOW_SAVINGS_RATE_THRESHOLD = 10  # Percentage
    RECOMMENDED_SAVINGS_RATE = 20  # Percentage
    RECOMMENDED_INVESTMENT_RATE = 15  # Percentage
    
    # UI settings
    MAX_RECENT_TRANSACTIONS = 10
    DEFAULT_CATEGORY = "General"
    
    # Export formatting
    EXCEL_HEADER_COLOR = "366092"
    EXCEL_CURRENCY_FORMAT = '"$"#,##0.00'
    EXCEL_PERCENTAGE_FORMAT = '0.00%'
    EXCEL_DATE_FORMAT = 'yyyy-mm-dd'
    
    @classmethod
    def ensure_directories(cls):
        """Ensure all required directories exist."""
        directories = [
            cls.DATA_DIR,
            cls.EXPORTS_DIR,
            cls.LOGS_DIR,
            cls.CONFIG_DIR,
            cls.BACKUP_DIR
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def get_backup_filename(cls, timestamp=None):
        """Generate backup filename with timestamp."""
        from datetime import datetime
        if timestamp is None:
            timestamp = datetime.now()
        
        return cls.BACKUP_DIR / f"financial_data_backup_{timestamp.strftime('%Y%m%d_%H%M%S')}.json"
    
    @classmethod
    def get_export_filename(cls, export_type="excel", timestamp=None):
        """Generate export filename with timestamp."""
        from datetime import datetime
        if timestamp is None:
            timestamp = datetime.now()
        
        if export_type == "excel":
            extension = "xlsx"
        elif export_type == "csv":
            extension = "csv"
        else:
            extension = "txt"
        
        return cls.EXPORTS_DIR / f"financial_data_{timestamp.strftime('%Y%m%d_%H%M%S')}.{extension}"
