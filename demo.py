#!/usr/bin/env python3
"""
Demo script to showcase the Financial Content Creation Application.
"""

import sys
import os
from datetime import datetime, timedelta
from pathlib import Path

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.models.financial_records import Income, Expense, Investment, Budget
from src.utils.calculations import FinancialAnalyzer
from src.exporters.excel_exporter import ExcelExporter
from src.utils.dashboard_generator import DashboardGenerator

def create_sample_data():
    """Create sample financial data for demonstration."""
    records = []
    
    # Sample Income Records
    incomes = [
        Income(5000, "Monthly Salary", source="ABC Company", category="Salary", is_recurring=True, frequency="monthly"),
        Income(1200, "Freelance Project", source="XYZ Client", category="Freelance"),
        Income(500, "Investment Dividends", source="Stock Portfolio", category="Investment Income"),
        Income(200, "Side Business", source="Online Store", category="Business"),
    ]
    records.extend(incomes)
    
    # Sample Expense Records
    expenses = [
        Expense(1200, "Rent", vendor="Property Management", category="Housing", is_essential=True),
        Expense(300, "Groceries", vendor="Supermarket", category="Food", is_essential=True),
        Expense(150, "Utilities", vendor="Electric Company", category="Utilities", is_essential=True),
        Expense(80, "Internet", vendor="ISP", category="Utilities", is_essential=True),
        Expense(200, "Dining Out", vendor="Various Restaurants", category="Food", is_essential=False),
        Expense(100, "Entertainment", vendor="Movie Theater", category="Entertainment", is_essential=False),
        Expense(250, "Car Payment", vendor="Auto Loan", category="Transportation", is_essential=True),
        Expense(120, "Gas", vendor="Gas Station", category="Transportation", is_essential=True),
        Expense(50, "Gym Membership", vendor="Fitness Center", category="Health", is_essential=False),
    ]
    records.extend(expenses)
    
    # Sample Investment Records
    investments = [
        Investment(1000, "S&P 500 ETF", investment_type="ETF", symbol="SPY", shares=2.5, price_per_share=400, category="Stocks"),
        Investment(500, "Tech Stock", investment_type="Stock", symbol="AAPL", shares=3, price_per_share=166.67, category="Stocks"),
        Investment(2000, "401k Contribution", investment_type="Retirement", category="Retirement"),
        Investment(300, "Cryptocurrency", investment_type="Crypto", symbol="BTC", category="Alternative"),
    ]
    records.extend(investments)
    
    # Sample Budget Records
    budgets = [
        Budget(1500, "Housing Budget", period="Monthly", category="Housing", spent=1200),
        Budget(400, "Food Budget", period="Monthly", category="Food", spent=500),  # Over budget
        Budget(300, "Entertainment Budget", period="Monthly", category="Entertainment", spent=100),
        Budget(400, "Transportation Budget", period="Monthly", category="Transportation", spent=370),
    ]
    records.extend(budgets)
    
    return records

def demonstrate_calculations(records):
    """Demonstrate financial calculations."""
    print("\n" + "="*60)
    print("FINANCIAL CALCULATIONS DEMONSTRATION")
    print("="*60)
    
    analyzer = FinancialAnalyzer(records)
    summary = analyzer.get_summary_statistics()
    
    print(f"\nOverall Summary:")
    print(f"  Total Income: ${summary['total_income']:,.2f}")
    print(f"  Total Expenses: ${summary['total_expenses']:,.2f}")
    print(f"  Total Investments: ${summary['total_investments']:,.2f}")
    print(f"  Net Income: ${summary['net_income']:,.2f}")
    print(f"  Savings Rate: {summary['savings_rate']:.1f}%")
    
    # Category breakdown
    expense_categories = analyzer.get_category_breakdown('expense')
    print(f"\nExpense Categories:")
    for category, data in expense_categories.items():
        print(f"  {category}: ${data['total']:,.2f} ({data['percentage']:.1f}%)")
    
    # Budget analysis
    budget_analysis = analyzer.get_budget_analysis()
    print(f"\nBudget Analysis:")
    print(f"  Total Budgeted: ${budget_analysis['total_budgeted']:,.2f}")
    print(f"  Total Spent: ${budget_analysis['total_spent']:,.2f}")
    print(f"  Average Utilization: {budget_analysis['average_utilization']:.1f}%")
    print(f"  Over Budget Count: {budget_analysis['over_budget_count']}")

def demonstrate_excel_export(records):
    """Demonstrate Excel export functionality."""
    print("\n" + "="*60)
    print("EXCEL EXPORT DEMONSTRATION")
    print("="*60)
    
    try:
        # Create exports directory
        Path('exports').mkdir(exist_ok=True)
        
        # Export to Excel
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"exports/demo_financial_data_{timestamp}.xlsx"
        
        exporter = ExcelExporter()
        exporter.export(records, filename)
        
        print(f"\n✓ Excel file created successfully: {filename}")
        print(f"  File contains {len(records)} records across multiple worksheets")
        print(f"  Includes summary dashboard with charts and analysis")
        
    except Exception as e:
        print(f"\n✗ Excel export failed: {e}")

def demonstrate_dashboard(records):
    """Demonstrate dashboard generation."""
    print("\n" + "="*60)
    print("DASHBOARD DEMONSTRATION")
    print("="*60)
    
    try:
        generator = DashboardGenerator(records)
        
        # Generate dashboard data
        dashboard_data = generator.generate_dashboard_data()
        
        print(f"\nDashboard Overview:")
        print(f"  Total Records: {dashboard_data['quick_stats']['total_records']}")
        print(f"  Essential Expenses: ${dashboard_data['quick_stats']['essential_expenses']:,.2f}")
        print(f"  Non-Essential Expenses: ${dashboard_data['quick_stats']['non_essential_expenses']:,.2f}")
        print(f"  Average Transaction: ${dashboard_data['quick_stats']['average_transaction']:,.2f}")
        
        # Show alerts
        if dashboard_data['alerts']:
            print(f"\nAlerts:")
            for alert in dashboard_data['alerts']:
                print(f"  {alert['type'].upper()}: {alert['message']}")
        
        # Export HTML dashboard
        html_filename = f"exports/dashboard_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        if generator.export_dashboard_html(html_filename):
            print(f"\n✓ HTML dashboard created: {html_filename}")
        
    except Exception as e:
        print(f"\n✗ Dashboard generation failed: {e}")

def demonstrate_google_sheets():
    """Demonstrate Google Sheets integration setup."""
    print("\n" + "="*60)
    print("GOOGLE SHEETS INTEGRATION")
    print("="*60)
    
    try:
        from src.exporters.google_sheets_exporter import GoogleSheetsExporter, GOOGLE_SHEETS_AVAILABLE
        
        if GOOGLE_SHEETS_AVAILABLE:
            print("\n✓ Google Sheets libraries are available")
            
            # Create credentials template
            template_file = GoogleSheetsExporter.setup_credentials_template()
            print(f"✓ Credentials template created: {template_file}")
            print("\nTo use Google Sheets export:")
            print("1. Go to Google Cloud Console")
            print("2. Create a service account and download credentials")
            print("3. Save credentials as 'config/google_credentials.json'")
            print("4. Use the Google Sheets export option in the application")
        else:
            print("\n⚠ Google Sheets libraries not installed")
            print("Install with: pip install gspread google-auth")
            
    except Exception as e:
        print(f"\n✗ Google Sheets setup failed: {e}")

def main():
    """Run the demonstration."""
    print("Financial Content Creation Application - DEMO")
    print("=" * 60)
    print("This demo showcases the key features of the application")
    print("with sample financial data.")
    
    # Create sample data
    print("\nCreating sample financial data...")
    records = create_sample_data()
    print(f"✓ Created {len(records)} sample records")
    
    # Demonstrate features
    demonstrate_calculations(records)
    demonstrate_excel_export(records)
    demonstrate_dashboard(records)
    demonstrate_google_sheets()
    
    print("\n" + "="*60)
    print("DEMO COMPLETED")
    print("="*60)
    print("\nTo run the full application:")
    print("  python main.py          # CLI interface")
    print("  python main.py --gui    # GUI interface")
    print("\nCheck the 'exports/' directory for generated files.")

if __name__ == "__main__":
    main()
