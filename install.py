#!/usr/bin/env python3
"""
Installation script for the Financial Content Creation Application.
"""

import subprocess
import sys
import os
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required.")
        print(f"Current version: {sys.version}")
        return False
    print(f"✓ Python version {sys.version_info.major}.{sys.version_info.minor} is compatible")
    return True

def install_requirements():
    """Install required packages."""
    print("\nInstalling required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ All required packages installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install packages: {e}")
        return False

def create_directories():
    """Create necessary directories."""
    print("\nCreating application directories...")
    directories = [
        'data',
        'exports', 
        'logs',
        'config',
        'data/backups'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✓ Created directory: {directory}")

def setup_config():
    """Set up configuration files."""
    print("\nSetting up configuration...")
    
    # Create config directory
    config_dir = Path('config')
    config_dir.mkdir(exist_ok=True)
    
    # Create sample configuration file
    sample_config = config_dir / 'sample_config.txt'
    with open(sample_config, 'w') as f:
        f.write("""
Financial Content Creation Application - Configuration

1. For Google Sheets integration:
   - Place your Google service account credentials in: config/google_credentials.json
   - Enable Google Sheets API and Google Drive API in Google Cloud Console

2. Data files:
   - Application data: data/financial_data.json
   - Backups: data/backups/
   - Exports: exports/

3. Logs:
   - Application logs: logs/

4. To run the application:
   - CLI: python main.py
   - GUI: python main.py --gui
   - Demo: python demo.py
""")
    
    print(f"✓ Created configuration guide: {sample_config}")

def test_installation():
    """Test if the installation works."""
    print("\nTesting installation...")
    
    try:
        # Test imports
        sys.path.append('src')
        from src.models.financial_records import Income, Expense
        from src.utils.calculations import FinancialCalculator
        from src.exporters.excel_exporter import ExcelExporter
        
        # Test basic functionality
        income = Income(1000, "Test Income")
        expense = Expense(500, "Test Expense")
        
        total = FinancialCalculator.calculate_total([income, expense])
        
        print("✓ Core modules imported successfully")
        print("✓ Basic functionality tested")
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Test failed: {e}")
        return False

def main():
    """Main installation function."""
    print("Financial Content Creation Application - Installer")
    print("=" * 60)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install requirements
    if not install_requirements():
        print("\nInstallation failed. Please check the error messages above.")
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Setup configuration
    setup_config()
    
    # Test installation
    if not test_installation():
        print("\nInstallation completed but tests failed.")
        print("The application may not work correctly.")
        sys.exit(1)
    
    print("\n" + "=" * 60)
    print("INSTALLATION COMPLETED SUCCESSFULLY!")
    print("=" * 60)
    print("\nNext steps:")
    print("1. Run the demo: python demo.py")
    print("2. Start the application: python main.py")
    print("3. For GUI interface: python main.py --gui")
    print("\nFor Google Sheets integration:")
    print("- Set up Google service account credentials")
    print("- Save as config/google_credentials.json")
    print("\nCheck README.md for detailed usage instructions.")

if __name__ == "__main__":
    main()
