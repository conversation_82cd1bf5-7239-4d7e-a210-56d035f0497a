#!/usr/bin/env python3
"""
Financial Content Creation Application
Main entry point for the financial data management and export system.
"""

import sys
import os
from datetime import datetime
from pathlib import Path

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.ui.cli_interface import CLIInterface
from src.utils.logger import setup_logger

def main():
    """Main application entry point."""
    print("=" * 60)
    print("Financial Content Creation Application")
    print("=" * 60)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # Setup logging
    logger = setup_logger()
    logger.info("Application started")

    try:
        # Create necessary directories
        create_directories()

        # Check for GUI option
        if len(sys.argv) > 1 and sys.argv[1] == "--gui":
            try:
                from src.ui.gui_interface import FinancialGUI
                print("Starting GUI interface...")
                app = FinancialGUI()
                app.run()
            except ImportError:
                print("GUI interface not available. Please install tkinter.")
                print("Falling back to CLI interface...")
                cli = CLIInterface()
                cli.run()
        else:
            # Show interface options
            print("Choose interface:")
            print("1. Command Line Interface (CLI)")
            print("2. Graphical User Interface (GUI)")

            choice = input("\nEnter your choice (1 or 2, default is 1): ").strip()

            if choice == "2":
                try:
                    from src.ui.gui_interface import FinancialGUI
                    print("Starting GUI interface...")
                    app = FinancialGUI()
                    app.run()
                except ImportError:
                    print("GUI interface not available. Please install tkinter.")
                    print("Starting CLI interface...")
                    cli = CLIInterface()
                    cli.run()
            else:
                # Default to CLI
                cli = CLIInterface()
                cli.run()

    except KeyboardInterrupt:
        print("\n\nApplication interrupted by user.")
        logger.info("Application interrupted by user")
    except Exception as e:
        print(f"\nAn error occurred: {e}")
        logger.error(f"Application error: {e}")
        sys.exit(1)
    finally:
        logger.info("Application ended")

def create_directories():
    """Create necessary directories for the application."""
    directories = [
        'data',
        'exports',
        'logs',
        'config'
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)

if __name__ == "__main__":
    main()
