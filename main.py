#!/usr/bin/env python3
"""
Financial Content Creation Application
Main entry point for the financial data management and export system.
"""

import sys
import os
from datetime import datetime
from pathlib import Path

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.ui.cli_interface import CLIInterface
from src.utils.logger import setup_logger

def main():
    """Main application entry point."""
    print("=" * 60)
    print("Financial Content Creation Application")
    print("=" * 60)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Setup logging
    logger = setup_logger()
    logger.info("Application started")
    
    try:
        # Create necessary directories
        create_directories()
        
        # Initialize and run the CLI interface
        cli = CLIInterface()
        cli.run()
        
    except KeyboardInterrupt:
        print("\n\nApplication interrupted by user.")
        logger.info("Application interrupted by user")
    except Exception as e:
        print(f"\nAn error occurred: {e}")
        logger.error(f"Application error: {e}")
        sys.exit(1)
    finally:
        logger.info("Application ended")

def create_directories():
    """Create necessary directories for the application."""
    directories = [
        'data',
        'exports',
        'logs',
        'config'
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)

if __name__ == "__main__":
    main()
