"""
Excel export functionality for financial data.
"""

from datetime import datetime
from typing import List, Any, Dict
from pathlib import Path
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from openpyxl.chart import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Reference

from ..models.financial_records import Income, Expense, Investment, Budget
from ..utils.calculations import FinancialAnalyzer, FinancialCalculator
from ..utils.logger import setup_logger

class ExcelExporter:
    """Excel export functionality with formatting and charts."""
    
    def __init__(self):
        """Initialize the Excel exporter."""
        self.logger = setup_logger()
        self.workbook = None
        
        # Define styles
        self.header_font = Font(bold=True, color="FFFFFF")
        self.header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        self.currency_format = '"$"#,##0.00'
        self.percentage_format = '0.00%'
        self.date_format = 'yyyy-mm-dd'
        
        # Border styles
        self.thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
    
    def export(self, records: List[Any], filepath: str):
        """
        Export financial records to Excel file.
        
        Args:
            records: List of financial records
            filepath: Output file path
        """
        try:
            self.workbook = openpyxl.Workbook()
            
            # Remove default sheet
            self.workbook.remove(self.workbook.active)
            
            # Create worksheets
            self._create_summary_sheet(records)
            self._create_income_sheet(records)
            self._create_expense_sheet(records)
            self._create_investment_sheet(records)
            self._create_budget_sheet(records)
            
            # Save workbook
            Path(filepath).parent.mkdir(parents=True, exist_ok=True)
            self.workbook.save(filepath)
            
            self.logger.info(f"Excel file exported successfully: {filepath}")
            
        except Exception as e:
            self.logger.error(f"Excel export error: {e}")
            raise
    
    def _create_summary_sheet(self, records: List[Any]):
        """Create summary dashboard sheet."""
        ws = self.workbook.create_sheet("Summary Dashboard", 0)
        
        # Title
        ws['A1'] = "Financial Summary Dashboard"
        ws['A1'].font = Font(size=16, bold=True)
        ws.merge_cells('A1:F1')
        
        # Export date
        ws['A2'] = f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        ws['A2'].font = Font(italic=True)
        
        # Analyze data
        analyzer = FinancialAnalyzer(records)
        summary = analyzer.get_summary_statistics()
        
        # Overall summary section
        row = 4
        ws[f'A{row}'] = "Overall Summary"
        ws[f'A{row}'].font = Font(size=14, bold=True)
        
        row += 1
        summary_data = [
            ("Total Income", summary['total_income']),
            ("Total Expenses", summary['total_expenses']),
            ("Total Investments", summary['total_investments']),
            ("Net Income", summary['net_income']),
            ("Savings Rate", summary['savings_rate'] / 100)
        ]
        
        for label, value in summary_data:
            ws[f'A{row}'] = label
            ws[f'B{row}'] = value
            if "Rate" in label:
                ws[f'B{row}'].number_format = self.percentage_format
            else:
                ws[f'B{row}'].number_format = self.currency_format
            row += 1
        
        # Record counts section
        row += 1
        ws[f'A{row}'] = "Record Counts"
        ws[f'A{row}'].font = Font(size=14, bold=True)
        
        row += 1
        for record_type, count in summary['record_count'].items():
            ws[f'A{row}'] = record_type.title()
            ws[f'B{row}'] = count
            row += 1
        
        # Category breakdown
        expense_categories = analyzer.get_category_breakdown('expense')
        if expense_categories:
            row += 1
            ws[f'A{row}'] = "Expense Categories"
            ws[f'A{row}'].font = Font(size=14, bold=True)
            
            row += 1
            ws[f'A{row}'] = "Category"
            ws[f'B{row}'] = "Amount"
            ws[f'C{row}'] = "Percentage"
            self._apply_header_style(ws, row, 'A', 'C')
            
            row += 1
            for category, data in expense_categories.items():
                ws[f'A{row}'] = category
                ws[f'B{row}'] = data['total']
                ws[f'C{row}'] = data['percentage'] / 100
                ws[f'B{row}'].number_format = self.currency_format
                ws[f'C{row}'].number_format = self.percentage_format
                row += 1
        
        # Budget analysis
        budget_analysis = analyzer.get_budget_analysis()
        if budget_analysis:
            row += 1
            ws[f'A{row}'] = "Budget Analysis"
            ws[f'A{row}'].font = Font(size=14, bold=True)
            
            row += 1
            budget_summary = [
                ("Total Budgeted", budget_analysis['total_budgeted']),
                ("Total Spent", budget_analysis['total_spent']),
                ("Average Utilization", budget_analysis['average_utilization'] / 100),
                ("Over Budget Count", budget_analysis['over_budget_count'])
            ]
            
            for label, value in budget_summary:
                ws[f'A{row}'] = label
                ws[f'B{row}'] = value
                if "Utilization" in label:
                    ws[f'B{row}'].number_format = self.percentage_format
                elif "Count" not in label:
                    ws[f'B{row}'].number_format = self.currency_format
                row += 1
        
        # Auto-adjust column widths
        self._auto_adjust_columns(ws)
    
    def _create_income_sheet(self, records: List[Any]):
        """Create income records sheet."""
        incomes = [r for r in records if isinstance(r, Income)]
        if not incomes:
            return
        
        ws = self.workbook.create_sheet("Income Records")
        
        # Headers
        headers = ["Date", "Description", "Amount", "Source", "Category", "Recurring", "Frequency"]
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            self._apply_header_style_cell(cell)
        
        # Data
        for row, income in enumerate(incomes, 2):
            ws.cell(row=row, column=1, value=income.date.date()).number_format = self.date_format
            ws.cell(row=row, column=2, value=income.description)
            ws.cell(row=row, column=3, value=float(income.amount)).number_format = self.currency_format
            ws.cell(row=row, column=4, value=income.source)
            ws.cell(row=row, column=5, value=income.category)
            ws.cell(row=row, column=6, value="Yes" if income.is_recurring else "No")
            ws.cell(row=row, column=7, value=income.frequency or "N/A")
        
        # Add total row
        total_row = len(incomes) + 2
        ws.cell(row=total_row, column=2, value="TOTAL").font = Font(bold=True)
        ws.cell(row=total_row, column=3, value=f"=SUM(C2:C{len(incomes)+1})").number_format = self.currency_format
        ws.cell(row=total_row, column=3).font = Font(bold=True)
        
        self._auto_adjust_columns(ws)
    
    def _create_expense_sheet(self, records: List[Any]):
        """Create expense records sheet."""
        expenses = [r for r in records if isinstance(r, Expense)]
        if not expenses:
            return
        
        ws = self.workbook.create_sheet("Expense Records")
        
        # Headers
        headers = ["Date", "Description", "Amount", "Vendor", "Category", "Payment Method", "Essential"]
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            self._apply_header_style_cell(cell)
        
        # Data
        for row, expense in enumerate(expenses, 2):
            ws.cell(row=row, column=1, value=expense.date.date()).number_format = self.date_format
            ws.cell(row=row, column=2, value=expense.description)
            ws.cell(row=row, column=3, value=float(expense.amount)).number_format = self.currency_format
            ws.cell(row=row, column=4, value=expense.vendor)
            ws.cell(row=row, column=5, value=expense.category)
            ws.cell(row=row, column=6, value=expense.payment_method)
            ws.cell(row=row, column=7, value="Yes" if expense.is_essential else "No")
        
        # Add total row
        total_row = len(expenses) + 2
        ws.cell(row=total_row, column=2, value="TOTAL").font = Font(bold=True)
        ws.cell(row=total_row, column=3, value=f"=SUM(C2:C{len(expenses)+1})").number_format = self.currency_format
        ws.cell(row=total_row, column=3).font = Font(bold=True)
        
        self._auto_adjust_columns(ws)
    
    def _create_investment_sheet(self, records: List[Any]):
        """Create investment records sheet."""
        investments = [r for r in records if isinstance(r, Investment)]
        if not investments:
            return
        
        ws = self.workbook.create_sheet("Investment Records")
        
        # Headers
        headers = ["Date", "Description", "Amount", "Type", "Symbol", "Shares", "Price/Share", "Category"]
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            self._apply_header_style_cell(cell)
        
        # Data
        for row, investment in enumerate(investments, 2):
            ws.cell(row=row, column=1, value=investment.date.date()).number_format = self.date_format
            ws.cell(row=row, column=2, value=investment.description)
            ws.cell(row=row, column=3, value=float(investment.amount)).number_format = self.currency_format
            ws.cell(row=row, column=4, value=investment.investment_type)
            ws.cell(row=row, column=5, value=investment.symbol or "N/A")
            ws.cell(row=row, column=6, value=investment.shares or "N/A")
            if investment.price_per_share:
                ws.cell(row=row, column=7, value=investment.price_per_share).number_format = self.currency_format
            else:
                ws.cell(row=row, column=7, value="N/A")
            ws.cell(row=row, column=8, value=investment.category)
        
        # Add total row
        total_row = len(investments) + 2
        ws.cell(row=total_row, column=2, value="TOTAL").font = Font(bold=True)
        ws.cell(row=total_row, column=3, value=f"=SUM(C2:C{len(investments)+1})").number_format = self.currency_format
        ws.cell(row=total_row, column=3).font = Font(bold=True)
        
        self._auto_adjust_columns(ws)

    def _create_budget_sheet(self, records: List[Any]):
        """Create budget records sheet."""
        budgets = [r for r in records if isinstance(r, Budget)]
        if not budgets:
            return

        ws = self.workbook.create_sheet("Budget Records")

        # Headers
        headers = ["Date", "Description", "Budgeted", "Spent", "Remaining", "Utilization", "Period", "Category", "Over Budget"]
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            self._apply_header_style_cell(cell)

        # Data
        for row, budget in enumerate(budgets, 2):
            ws.cell(row=row, column=1, value=budget.date.date()).number_format = self.date_format
            ws.cell(row=row, column=2, value=budget.description)
            ws.cell(row=row, column=3, value=float(budget.amount)).number_format = self.currency_format
            ws.cell(row=row, column=4, value=float(budget.spent)).number_format = self.currency_format
            ws.cell(row=row, column=5, value=float(budget.get_remaining())).number_format = self.currency_format
            ws.cell(row=row, column=6, value=budget.get_utilization_percentage() / 100).number_format = self.percentage_format
            ws.cell(row=row, column=7, value=budget.period)
            ws.cell(row=row, column=8, value=budget.category)
            ws.cell(row=row, column=9, value="Yes" if budget.is_over_budget() else "No")

        # Add total row
        total_row = len(budgets) + 2
        ws.cell(row=total_row, column=2, value="TOTAL").font = Font(bold=True)
        ws.cell(row=total_row, column=3, value=f"=SUM(C2:C{len(budgets)+1})").number_format = self.currency_format
        ws.cell(row=total_row, column=3).font = Font(bold=True)
        ws.cell(row=total_row, column=4, value=f"=SUM(D2:D{len(budgets)+1})").number_format = self.currency_format
        ws.cell(row=total_row, column=4).font = Font(bold=True)

        self._auto_adjust_columns(ws)

    def _apply_header_style(self, ws, row: int, start_col: str, end_col: str):
        """Apply header styling to a range of cells."""
        for col in range(ord(start_col), ord(end_col) + 1):
            cell = ws[f'{chr(col)}{row}']
            self._apply_header_style_cell(cell)

    def _apply_header_style_cell(self, cell):
        """Apply header styling to a single cell."""
        cell.font = self.header_font
        cell.fill = self.header_fill
        cell.alignment = Alignment(horizontal='center', vertical='center')
        cell.border = self.thin_border

    def _auto_adjust_columns(self, ws):
        """Auto-adjust column widths based on content."""
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)

            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass

            adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
            ws.column_dimensions[column_letter].width = adjusted_width
