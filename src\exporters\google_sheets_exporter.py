"""
Google Sheets export functionality for financial data.
"""

import os
import json
from datetime import datetime
from typing import List, Any, Dict, Optional
from pathlib import Path

try:
    import gspread
    from google.oauth2.service_account import Credentials
    GOOGLE_SHEETS_AVAILABLE = True
except ImportError:
    GOOGLE_SHEETS_AVAILABLE = False

from ..models.financial_records import Income, Expense, Investment, Budget
from ..utils.calculations import FinancialAnalyzer
from ..utils.logger import setup_logger

class GoogleSheetsExporter:
    """Google Sheets export functionality."""
    
    def __init__(self, credentials_file: Optional[str] = None):
        """
        Initialize the Google Sheets exporter.
        
        Args:
            credentials_file: Path to Google service account credentials JSON file
        """
        self.logger = setup_logger()
        self.credentials_file = credentials_file or 'config/google_credentials.json'
        self.client = None
        self.scopes = [
            'https://www.googleapis.com/auth/spreadsheets',
            'https://www.googleapis.com/auth/drive'
        ]
        
        if not GOOGLE_SHEETS_AVAILABLE:
            self.logger.warning("Google Sheets libraries not available. Install with: pip install gspread google-auth")
    
    def authenticate(self) -> bool:
        """
        Authenticate with Google Sheets API.
        
        Returns:
            bool: True if authentication successful, False otherwise
        """
        if not GOOGLE_SHEETS_AVAILABLE:
            self.logger.error("Google Sheets libraries not installed")
            return False
        
        try:
            if not Path(self.credentials_file).exists():
                self.logger.error(f"Credentials file not found: {self.credentials_file}")
                return False
            
            credentials = Credentials.from_service_account_file(
                self.credentials_file, 
                scopes=self.scopes
            )
            self.client = gspread.authorize(credentials)
            self.logger.info("Successfully authenticated with Google Sheets API")
            return True
            
        except Exception as e:
            self.logger.error(f"Google Sheets authentication failed: {e}")
            return False
    
    def export(self, records: List[Any], spreadsheet_name: str, 
               share_with_emails: Optional[List[str]] = None) -> Optional[str]:
        """
        Export financial records to Google Sheets.
        
        Args:
            records: List of financial records
            spreadsheet_name: Name for the new spreadsheet
            share_with_emails: List of email addresses to share the spreadsheet with
            
        Returns:
            str: URL of the created spreadsheet, or None if failed
        """
        if not self.authenticate():
            return None
        
        try:
            # Create new spreadsheet
            spreadsheet = self.client.create(spreadsheet_name)
            self.logger.info(f"Created spreadsheet: {spreadsheet_name}")
            
            # Remove default sheet
            default_sheet = spreadsheet.sheet1
            
            # Create worksheets
            self._create_summary_sheet(spreadsheet, records)
            self._create_income_sheet(spreadsheet, records)
            self._create_expense_sheet(spreadsheet, records)
            self._create_investment_sheet(spreadsheet, records)
            self._create_budget_sheet(spreadsheet, records)
            
            # Delete default sheet
            spreadsheet.del_worksheet(default_sheet)
            
            # Share with specified emails
            if share_with_emails:
                for email in share_with_emails:
                    try:
                        spreadsheet.share(email, perm_type='user', role='writer')
                        self.logger.info(f"Shared spreadsheet with {email}")
                    except Exception as e:
                        self.logger.warning(f"Failed to share with {email}: {e}")
            
            url = spreadsheet.url
            self.logger.info(f"Google Sheets export completed: {url}")
            return url
            
        except Exception as e:
            self.logger.error(f"Google Sheets export failed: {e}")
            return None
    
    def _create_summary_sheet(self, spreadsheet, records: List[Any]):
        """Create summary dashboard sheet."""
        try:
            worksheet = spreadsheet.add_worksheet(title="Summary Dashboard", rows=100, cols=10)
            
            # Analyze data
            analyzer = FinancialAnalyzer(records)
            summary = analyzer.get_summary_statistics()
            
            # Prepare data
            data = []
            data.append(["Financial Summary Dashboard"])
            data.append([f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"])
            data.append([])  # Empty row
            
            # Overall summary
            data.append(["Overall Summary"])
            data.append(["Total Income", f"${summary['total_income']:,.2f}"])
            data.append(["Total Expenses", f"${summary['total_expenses']:,.2f}"])
            data.append(["Total Investments", f"${summary['total_investments']:,.2f}"])
            data.append(["Net Income", f"${summary['net_income']:,.2f}"])
            data.append(["Savings Rate", f"{summary['savings_rate']:.1f}%"])
            data.append([])  # Empty row
            
            # Record counts
            data.append(["Record Counts"])
            for record_type, count in summary['record_count'].items():
                data.append([record_type.title(), count])
            
            # Category breakdown
            expense_categories = analyzer.get_category_breakdown('expense')
            if expense_categories:
                data.append([])  # Empty row
                data.append(["Expense Categories"])
                data.append(["Category", "Amount", "Percentage"])
                for category, cat_data in expense_categories.items():
                    data.append([category, f"${cat_data['total']:,.2f}", f"{cat_data['percentage']:.1f}%"])
            
            # Update worksheet
            worksheet.update('A1', data)
            
            # Format header
            worksheet.format('A1', {
                'textFormat': {'bold': True, 'fontSize': 16},
                'horizontalAlignment': 'CENTER'
            })
            
        except Exception as e:
            self.logger.error(f"Error creating summary sheet: {e}")
    
    def _create_income_sheet(self, spreadsheet, records: List[Any]):
        """Create income records sheet."""
        incomes = [r for r in records if isinstance(r, Income)]
        if not incomes:
            return
        
        try:
            worksheet = spreadsheet.add_worksheet(title="Income Records", rows=len(incomes)+10, cols=8)
            
            # Headers
            headers = ["Date", "Description", "Amount", "Source", "Category", "Recurring", "Frequency"]
            data = [headers]
            
            # Data rows
            for income in incomes:
                row = [
                    income.date.strftime('%Y-%m-%d'),
                    income.description,
                    float(income.amount),
                    income.source,
                    income.category,
                    "Yes" if income.is_recurring else "No",
                    income.frequency or "N/A"
                ]
                data.append(row)
            
            # Total row
            total_formula = f"=SUM(C2:C{len(incomes)+1})"
            data.append(["", "TOTAL", total_formula, "", "", "", ""])
            
            # Update worksheet
            worksheet.update('A1', data)
            
            # Format headers
            worksheet.format('A1:G1', {
                'textFormat': {'bold': True},
                'backgroundColor': {'red': 0.2, 'green': 0.4, 'blue': 0.6}
            })
            
        except Exception as e:
            self.logger.error(f"Error creating income sheet: {e}")
    
    def _create_expense_sheet(self, spreadsheet, records: List[Any]):
        """Create expense records sheet."""
        expenses = [r for r in records if isinstance(r, Expense)]
        if not expenses:
            return
        
        try:
            worksheet = spreadsheet.add_worksheet(title="Expense Records", rows=len(expenses)+10, cols=8)
            
            # Headers
            headers = ["Date", "Description", "Amount", "Vendor", "Category", "Payment Method", "Essential"]
            data = [headers]
            
            # Data rows
            for expense in expenses:
                row = [
                    expense.date.strftime('%Y-%m-%d'),
                    expense.description,
                    float(expense.amount),
                    expense.vendor,
                    expense.category,
                    expense.payment_method,
                    "Yes" if expense.is_essential else "No"
                ]
                data.append(row)
            
            # Total row
            total_formula = f"=SUM(C2:C{len(expenses)+1})"
            data.append(["", "TOTAL", total_formula, "", "", "", ""])
            
            # Update worksheet
            worksheet.update('A1', data)
            
            # Format headers
            worksheet.format('A1:G1', {
                'textFormat': {'bold': True},
                'backgroundColor': {'red': 0.2, 'green': 0.4, 'blue': 0.6}
            })
            
        except Exception as e:
            self.logger.error(f"Error creating expense sheet: {e}")
    
    def _create_investment_sheet(self, spreadsheet, records: List[Any]):
        """Create investment records sheet."""
        investments = [r for r in records if isinstance(r, Investment)]
        if not investments:
            return
        
        try:
            worksheet = spreadsheet.add_worksheet(title="Investment Records", rows=len(investments)+10, cols=9)
            
            # Headers
            headers = ["Date", "Description", "Amount", "Type", "Symbol", "Shares", "Price/Share", "Category"]
            data = [headers]
            
            # Data rows
            for investment in investments:
                row = [
                    investment.date.strftime('%Y-%m-%d'),
                    investment.description,
                    float(investment.amount),
                    investment.investment_type,
                    investment.symbol or "N/A",
                    investment.shares or "N/A",
                    investment.price_per_share or "N/A",
                    investment.category
                ]
                data.append(row)
            
            # Total row
            total_formula = f"=SUM(C2:C{len(investments)+1})"
            data.append(["", "TOTAL", total_formula, "", "", "", "", ""])
            
            # Update worksheet
            worksheet.update('A1', data)
            
            # Format headers
            worksheet.format('A1:H1', {
                'textFormat': {'bold': True},
                'backgroundColor': {'red': 0.2, 'green': 0.4, 'blue': 0.6}
            })
            
        except Exception as e:
            self.logger.error(f"Error creating investment sheet: {e}")
    
    def _create_budget_sheet(self, spreadsheet, records: List[Any]):
        """Create budget records sheet."""
        budgets = [r for r in records if isinstance(r, Budget)]
        if not budgets:
            return
        
        try:
            worksheet = spreadsheet.add_worksheet(title="Budget Records", rows=len(budgets)+10, cols=10)
            
            # Headers
            headers = ["Date", "Description", "Budgeted", "Spent", "Remaining", "Utilization", "Period", "Category", "Over Budget"]
            data = [headers]
            
            # Data rows
            for budget in budgets:
                row = [
                    budget.date.strftime('%Y-%m-%d'),
                    budget.description,
                    float(budget.amount),
                    float(budget.spent),
                    float(budget.get_remaining()),
                    f"{budget.get_utilization_percentage():.1f}%",
                    budget.period,
                    budget.category,
                    "Yes" if budget.is_over_budget() else "No"
                ]
                data.append(row)
            
            # Total row
            budgeted_formula = f"=SUM(C2:C{len(budgets)+1})"
            spent_formula = f"=SUM(D2:D{len(budgets)+1})"
            data.append(["", "TOTAL", budgeted_formula, spent_formula, "", "", "", "", ""])
            
            # Update worksheet
            worksheet.update('A1', data)
            
            # Format headers
            worksheet.format('A1:I1', {
                'textFormat': {'bold': True},
                'backgroundColor': {'red': 0.2, 'green': 0.4, 'blue': 0.6}
            })
            
        except Exception as e:
            self.logger.error(f"Error creating budget sheet: {e}")
    
    @staticmethod
    def setup_credentials_template():
        """Create a template for Google service account credentials."""
        template = *********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        
        config_dir = Path('config')
        config_dir.mkdir(exist_ok=True)
        
        template_file = config_dir / 'google_credentials_template.json'
        with open(template_file, 'w') as f:
            json.dump(template, f, indent=2)
        
        return str(template_file)
