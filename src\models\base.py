"""
Base classes for financial data models.
"""

from abc import ABC, abstractmethod
from datetime import datetime
from decimal import Decimal, InvalidOperation
from typing import Dict, Any, Optional
import uuid

class FinancialRecord(ABC):
    """Abstract base class for all financial records."""
    
    def __init__(self, amount: float, description: str, date: Optional[datetime] = None, 
                 category: str = "General", tags: Optional[list] = None):
        """
        Initialize a financial record.
        
        Args:
            amount (float): The monetary amount
            description (str): Description of the transaction
            date (datetime, optional): Date of the transaction
            category (str): Category for the transaction
            tags (list, optional): List of tags for categorization
        """
        self.id = str(uuid.uuid4())
        self.amount = self._validate_amount(amount)
        self.description = self._validate_description(description)
        self.date = date or datetime.now()
        self.category = category
        self.tags = tags or []
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
    
    def _validate_amount(self, amount: float) -> Decimal:
        """Validate and convert amount to Decimal for precision."""
        try:
            if amount < 0:
                raise ValueError("Amount cannot be negative")
            return Decimal(str(amount)).quantize(Decimal('0.01'))
        except (InvalidOperation, ValueError) as e:
            raise ValueError(f"Invalid amount: {amount}. {str(e)}")
    
    def _validate_description(self, description: str) -> str:
        """Validate description field."""
        if not description or not description.strip():
            raise ValueError("Description cannot be empty")
        return description.strip()
    
    def update(self, **kwargs):
        """Update record fields."""
        for key, value in kwargs.items():
            if hasattr(self, key):
                if key == 'amount':
                    value = self._validate_amount(value)
                elif key == 'description':
                    value = self._validate_description(value)
                setattr(self, key, value)
        self.updated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert record to dictionary."""
        return {
            'id': self.id,
            'amount': float(self.amount),
            'description': self.description,
            'date': self.date.isoformat(),
            'category': self.category,
            'tags': self.tags,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    @abstractmethod
    def get_type(self) -> str:
        """Return the type of financial record."""
        pass
    
    def __str__(self) -> str:
        return f"{self.get_type()}: ${self.amount} - {self.description}"
    
    def __repr__(self) -> str:
        return f"{self.__class__.__name__}(amount={self.amount}, description='{self.description}')"
