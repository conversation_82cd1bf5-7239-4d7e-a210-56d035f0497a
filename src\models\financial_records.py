"""
Specific financial record implementations.
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any
from .base import FinancialRecord

class Income(FinancialRecord):
    """Income record class."""
    
    def __init__(self, amount: float, description: str, source: str = "Unknown",
                 is_recurring: bool = False, frequency: Optional[str] = None, **kwargs):
        """
        Initialize an income record.
        
        Args:
            amount (float): Income amount
            description (str): Description of income
            source (str): Source of income
            is_recurring (bool): Whether this is recurring income
            frequency (str): Frequency if recurring (monthly, weekly, etc.)
        """
        super().__init__(amount, description, **kwargs)
        self.source = source
        self.is_recurring = is_recurring
        self.frequency = frequency
    
    def get_type(self) -> str:
        return "Income"
    
    def to_dict(self) -> Dict[str, Any]:
        data = super().to_dict()
        data.update({
            'source': self.source,
            'is_recurring': self.is_recurring,
            'frequency': self.frequency
        })
        return data

class Expense(FinancialRecord):
    """Expense record class."""
    
    def __init__(self, amount: float, description: str, vendor: str = "Unknown",
                 is_essential: bool = True, payment_method: str = "Cash", **kwargs):
        """
        Initialize an expense record.
        
        Args:
            amount (float): Expense amount
            description (str): Description of expense
            vendor (str): Vendor or payee
            is_essential (bool): Whether this is an essential expense
            payment_method (str): Method of payment
        """
        super().__init__(amount, description, **kwargs)
        self.vendor = vendor
        self.is_essential = is_essential
        self.payment_method = payment_method
    
    def get_type(self) -> str:
        return "Expense"
    
    def to_dict(self) -> Dict[str, Any]:
        data = super().to_dict()
        data.update({
            'vendor': self.vendor,
            'is_essential': self.is_essential,
            'payment_method': self.payment_method
        })
        return data

class Investment(FinancialRecord):
    """Investment record class."""
    
    def __init__(self, amount: float, description: str, investment_type: str = "Stock",
                 symbol: Optional[str] = None, shares: Optional[float] = None,
                 price_per_share: Optional[float] = None, **kwargs):
        """
        Initialize an investment record.
        
        Args:
            amount (float): Investment amount
            description (str): Description of investment
            investment_type (str): Type of investment (Stock, Bond, ETF, etc.)
            symbol (str): Stock/fund symbol
            shares (float): Number of shares
            price_per_share (float): Price per share
        """
        super().__init__(amount, description, **kwargs)
        self.investment_type = investment_type
        self.symbol = symbol
        self.shares = shares
        self.price_per_share = price_per_share
    
    def get_type(self) -> str:
        return "Investment"
    
    def calculate_current_value(self, current_price: float) -> Decimal:
        """Calculate current value based on current price."""
        if self.shares:
            return Decimal(str(self.shares * current_price)).quantize(Decimal('0.01'))
        return self.amount
    
    def to_dict(self) -> Dict[str, Any]:
        data = super().to_dict()
        data.update({
            'investment_type': self.investment_type,
            'symbol': self.symbol,
            'shares': self.shares,
            'price_per_share': self.price_per_share
        })
        return data

class Budget(FinancialRecord):
    """Budget record class."""
    
    def __init__(self, amount: float, description: str, period: str = "Monthly",
                 start_date: Optional[datetime] = None, end_date: Optional[datetime] = None,
                 spent: float = 0.0, **kwargs):
        """
        Initialize a budget record.
        
        Args:
            amount (float): Budget amount
            description (str): Description of budget
            period (str): Budget period (Monthly, Weekly, Yearly)
            start_date (datetime): Budget start date
            end_date (datetime): Budget end date
            spent (float): Amount already spent
        """
        super().__init__(amount, description, **kwargs)
        self.period = period
        self.start_date = start_date or datetime.now()
        self.end_date = end_date
        self.spent = Decimal(str(spent)).quantize(Decimal('0.01'))
    
    def get_type(self) -> str:
        return "Budget"
    
    def get_remaining(self) -> Decimal:
        """Get remaining budget amount."""
        return self.amount - self.spent
    
    def get_utilization_percentage(self) -> float:
        """Get budget utilization as percentage."""
        if self.amount == 0:
            return 0.0
        return float((self.spent / self.amount) * 100)
    
    def add_expense(self, expense_amount: float):
        """Add an expense to this budget."""
        self.spent += Decimal(str(expense_amount)).quantize(Decimal('0.01'))
        self.updated_at = datetime.now()
    
    def is_over_budget(self) -> bool:
        """Check if budget is exceeded."""
        return self.spent > self.amount
    
    def to_dict(self) -> Dict[str, Any]:
        data = super().to_dict()
        data.update({
            'period': self.period,
            'start_date': self.start_date.isoformat(),
            'end_date': self.end_date.isoformat() if self.end_date else None,
            'spent': float(self.spent),
            'remaining': float(self.get_remaining()),
            'utilization_percentage': self.get_utilization_percentage(),
            'is_over_budget': self.is_over_budget()
        })
        return data
