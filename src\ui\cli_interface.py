"""
Command Line Interface for the Financial Content Creation Application.
"""

import os
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path

from ..models.financial_records import Income, Expense, Investment, Budget
from ..utils.calculations import FinancialAnalyzer
from ..exporters.excel_exporter import ExcelExporter
from ..utils.logger import setup_logger

class CLIInterface:
    """Command Line Interface for financial data management."""
    
    def __init__(self):
        """Initialize the CLI interface."""
        self.logger = setup_logger()
        self.records: List[Any] = []
        self.data_file = Path('data/financial_data.json')
        self.load_data()
    
    def run(self):
        """Main CLI loop."""
        while True:
            self.display_main_menu()
            choice = input("\nEnter your choice: ").strip()
            
            try:
                if choice == '1':
                    self.add_income()
                elif choice == '2':
                    self.add_expense()
                elif choice == '3':
                    self.add_investment()
                elif choice == '4':
                    self.add_budget()
                elif choice == '5':
                    self.view_records()
                elif choice == '6':
                    self.view_summary()
                elif choice == '7':
                    self.export_data()
                elif choice == '8':
                    self.save_data()
                    print("Data saved successfully!")
                elif choice == '9':
                    self.save_data()
                    print("Thank you for using the Financial Content Creation Application!")
                    break
                else:
                    print("Invalid choice. Please try again.")
            except Exception as e:
                print(f"An error occurred: {e}")
                self.logger.error(f"CLI error: {e}")
    
    def display_main_menu(self):
        """Display the main menu."""
        print("\n" + "="*50)
        print("FINANCIAL CONTENT CREATION APPLICATION")
        print("="*50)
        print("1. Add Income")
        print("2. Add Expense")
        print("3. Add Investment")
        print("4. Add Budget")
        print("5. View Records")
        print("6. View Summary & Analysis")
        print("7. Export Data")
        print("8. Save Data")
        print("9. Exit")
        print("="*50)
    
    def add_income(self):
        """Add a new income record."""
        print("\n--- Add Income ---")
        try:
            amount = float(input("Amount: $"))
            description = input("Description: ")
            source = input("Source (optional): ") or "Unknown"
            category = input("Category (optional): ") or "General"
            
            is_recurring = input("Is this recurring income? (y/n): ").lower() == 'y'
            frequency = None
            if is_recurring:
                frequency = input("Frequency (monthly/weekly/yearly): ") or "monthly"
            
            income = Income(
                amount=amount,
                description=description,
                source=source,
                category=category,
                is_recurring=is_recurring,
                frequency=frequency
            )
            
            self.records.append(income)
            print(f"Income record added successfully! ID: {income.id}")
            
        except ValueError as e:
            print(f"Invalid input: {e}")
        except Exception as e:
            print(f"Error adding income: {e}")
    
    def add_expense(self):
        """Add a new expense record."""
        print("\n--- Add Expense ---")
        try:
            amount = float(input("Amount: $"))
            description = input("Description: ")
            vendor = input("Vendor/Payee (optional): ") or "Unknown"
            category = input("Category (optional): ") or "General"
            payment_method = input("Payment method (optional): ") or "Cash"
            
            is_essential = input("Is this an essential expense? (y/n): ").lower() == 'y'
            
            expense = Expense(
                amount=amount,
                description=description,
                vendor=vendor,
                category=category,
                payment_method=payment_method,
                is_essential=is_essential
            )
            
            self.records.append(expense)
            print(f"Expense record added successfully! ID: {expense.id}")
            
        except ValueError as e:
            print(f"Invalid input: {e}")
        except Exception as e:
            print(f"Error adding expense: {e}")
    
    def add_investment(self):
        """Add a new investment record."""
        print("\n--- Add Investment ---")
        try:
            amount = float(input("Amount: $"))
            description = input("Description: ")
            investment_type = input("Investment type (Stock/Bond/ETF/etc.): ") or "Stock"
            category = input("Category (optional): ") or "Investment"
            
            symbol = input("Symbol (optional): ") or None
            shares_input = input("Number of shares (optional): ")
            shares = float(shares_input) if shares_input else None
            
            price_input = input("Price per share (optional): ")
            price_per_share = float(price_input) if price_input else None
            
            investment = Investment(
                amount=amount,
                description=description,
                investment_type=investment_type,
                category=category,
                symbol=symbol,
                shares=shares,
                price_per_share=price_per_share
            )
            
            self.records.append(investment)
            print(f"Investment record added successfully! ID: {investment.id}")
            
        except ValueError as e:
            print(f"Invalid input: {e}")
        except Exception as e:
            print(f"Error adding investment: {e}")
    
    def add_budget(self):
        """Add a new budget record."""
        print("\n--- Add Budget ---")
        try:
            amount = float(input("Budget amount: $"))
            description = input("Description: ")
            category = input("Category: ") or "General"
            period = input("Period (Monthly/Weekly/Yearly): ") or "Monthly"
            
            spent_input = input("Amount already spent (optional): $")
            spent = float(spent_input) if spent_input else 0.0
            
            budget = Budget(
                amount=amount,
                description=description,
                category=category,
                period=period,
                spent=spent
            )
            
            self.records.append(budget)
            print(f"Budget record added successfully! ID: {budget.id}")
            
        except ValueError as e:
            print(f"Invalid input: {e}")
        except Exception as e:
            print(f"Error adding budget: {e}")
    
    def view_records(self):
        """View all records with filtering options."""
        if not self.records:
            print("\nNo records found.")
            return
        
        print("\n--- View Records ---")
        print("1. View All")
        print("2. View Income Only")
        print("3. View Expenses Only")
        print("4. View Investments Only")
        print("5. View Budgets Only")
        
        choice = input("Choose view option: ").strip()
        
        filtered_records = self.records
        if choice == '2':
            filtered_records = [r for r in self.records if isinstance(r, Income)]
        elif choice == '3':
            filtered_records = [r for r in self.records if isinstance(r, Expense)]
        elif choice == '4':
            filtered_records = [r for r in self.records if isinstance(r, Investment)]
        elif choice == '5':
            filtered_records = [r for r in self.records if isinstance(r, Budget)]
        
        if not filtered_records:
            print("No records found for the selected type.")
            return
        
        print(f"\n{len(filtered_records)} record(s) found:")
        print("-" * 80)
        for i, record in enumerate(filtered_records, 1):
            print(f"{i}. {record}")
            print(f"   Date: {record.date.strftime('%Y-%m-%d')}")
            print(f"   Category: {record.category}")
            if hasattr(record, 'source'):
                print(f"   Source: {record.source}")
            if hasattr(record, 'vendor'):
                print(f"   Vendor: {record.vendor}")
            print("-" * 80)

    def view_summary(self):
        """View financial summary and analysis."""
        if not self.records:
            print("\nNo records found for analysis.")
            return

        print("\n--- Financial Summary & Analysis ---")

        analyzer = FinancialAnalyzer(self.records)
        summary = analyzer.get_summary_statistics()

        print(f"\nOverall Summary:")
        print(f"Total Income: ${summary['total_income']:,.2f}")
        print(f"Total Expenses: ${summary['total_expenses']:,.2f}")
        print(f"Total Investments: ${summary['total_investments']:,.2f}")
        print(f"Net Income: ${summary['net_income']:,.2f}")
        print(f"Savings Rate: {summary['savings_rate']:.1f}%")

        print(f"\nRecord Counts:")
        for record_type, count in summary['record_count'].items():
            print(f"  {record_type.title()}: {count}")

        # Category breakdown
        expense_categories = analyzer.get_category_breakdown('expense')
        if expense_categories:
            print(f"\nExpense Categories:")
            for category, data in expense_categories.items():
                print(f"  {category}: ${data['total']:,.2f} ({data['percentage']:.1f}%)")

        # Budget analysis
        budget_analysis = analyzer.get_budget_analysis()
        if budget_analysis:
            print(f"\nBudget Analysis:")
            print(f"Total Budgeted: ${budget_analysis['total_budgeted']:,.2f}")
            print(f"Total Spent: ${budget_analysis['total_spent']:,.2f}")
            print(f"Average Utilization: {budget_analysis['average_utilization']:.1f}%")
            print(f"Over Budget Count: {budget_analysis['over_budget_count']}")

    def export_data(self):
        """Export data to various formats."""
        if not self.records:
            print("\nNo records to export.")
            return

        print("\n--- Export Data ---")
        print("1. Export to Excel (.xlsx)")
        print("2. Export to Google Sheets (Coming Soon)")

        choice = input("Choose export option: ").strip()

        if choice == '1':
            self.export_to_excel()
        elif choice == '2':
            print("Google Sheets export will be available in the next version.")
        else:
            print("Invalid choice.")

    def export_to_excel(self):
        """Export data to Excel file."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"financial_data_{timestamp}.xlsx"
            filepath = Path('exports') / filename

            exporter = ExcelExporter()
            exporter.export(self.records, str(filepath))

            print(f"Data exported successfully to: {filepath}")

        except Exception as e:
            print(f"Error exporting to Excel: {e}")
            self.logger.error(f"Excel export error: {e}")

    def save_data(self):
        """Save data to JSON file."""
        try:
            # Ensure data directory exists
            self.data_file.parent.mkdir(exist_ok=True)

            # Convert records to dictionaries
            data = {
                'records': [record.to_dict() for record in self.records],
                'last_saved': datetime.now().isoformat(),
                'version': '1.0'
            }

            with open(self.data_file, 'w') as f:
                json.dump(data, f, indent=2)

            self.logger.info(f"Data saved to {self.data_file}")

        except Exception as e:
            print(f"Error saving data: {e}")
            self.logger.error(f"Save error: {e}")

    def load_data(self):
        """Load data from JSON file."""
        try:
            if not self.data_file.exists():
                return

            with open(self.data_file, 'r') as f:
                data = json.load(f)

            # Reconstruct records from dictionaries
            for record_data in data.get('records', []):
                record = self._dict_to_record(record_data)
                if record:
                    self.records.append(record)

            print(f"Loaded {len(self.records)} records from {self.data_file}")
            self.logger.info(f"Data loaded from {self.data_file}")

        except Exception as e:
            print(f"Error loading data: {e}")
            self.logger.error(f"Load error: {e}")

    def _dict_to_record(self, data: Dict[str, Any]):
        """Convert dictionary back to financial record object."""
        try:
            record_type = None
            # Determine record type based on fields
            if 'source' in data:
                record_type = Income
            elif 'vendor' in data:
                record_type = Expense
            elif 'investment_type' in data:
                record_type = Investment
            elif 'period' in data:
                record_type = Budget

            if not record_type:
                return None

            # Create record with basic fields
            record = record_type(
                amount=data['amount'],
                description=data['description'],
                category=data.get('category', 'General')
            )

            # Set additional fields
            for key, value in data.items():
                if hasattr(record, key) and key not in ['amount', 'description', 'category']:
                    if key in ['date', 'created_at', 'updated_at', 'start_date', 'end_date']:
                        if value:
                            setattr(record, key, datetime.fromisoformat(value))
                    else:
                        setattr(record, key, value)

            return record

        except Exception as e:
            self.logger.error(f"Error converting dict to record: {e}")
            return None
