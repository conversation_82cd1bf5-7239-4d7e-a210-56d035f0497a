"""
Optional GUI interface using tkinter.
"""

try:
    import tkinter as tk
    from tkinter import ttk, messagebox, filedialog
    from tkinter.scrolledtext import ScrolledText
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False

from datetime import datetime
from typing import List, Any
import json
from pathlib import Path

from ..models.financial_records import Income, Expense, Investment, Budget
from ..utils.calculations import FinancialAnalyzer
from ..exporters.excel_exporter import ExcelExporter
from ..utils.logger import setup_logger

class FinancialGUI:
    """Simple GUI interface for the financial application."""
    
    def __init__(self):
        """Initialize the GUI."""
        if not GUI_AVAILABLE:
            raise ImportError("tkinter not available. GUI interface cannot be used.")
        
        self.logger = setup_logger()
        self.records: List[Any] = []
        self.data_file = Path('data/financial_data.json')
        
        # Create main window
        self.root = tk.Tk()
        self.root.title("Financial Content Creation Application")
        self.root.geometry("800x600")
        
        # Load data
        self.load_data()
        
        # Create GUI elements
        self.create_widgets()
        
    def create_widgets(self):
        """Create and arrange GUI widgets."""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create tabs
        self.create_overview_tab()
        self.create_add_record_tab()
        self.create_records_tab()
        self.create_export_tab()
        
        # Create status bar
        self.status_var = tk.StringVar()
        self.status_var.set(f"Loaded {len(self.records)} records")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def create_overview_tab(self):
        """Create overview/dashboard tab."""
        overview_frame = ttk.Frame(self.notebook)
        self.notebook.add(overview_frame, text="Overview")
        
        # Title
        title_label = ttk.Label(overview_frame, text="Financial Overview", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # Summary frame
        summary_frame = ttk.LabelFrame(overview_frame, text="Summary Statistics")
        summary_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Create summary labels
        self.summary_labels = {}
        summary_items = [
            ("Total Income", "total_income"),
            ("Total Expenses", "total_expenses"),
            ("Net Income", "net_income"),
            ("Savings Rate", "savings_rate")
        ]
        
        for i, (label_text, key) in enumerate(summary_items):
            row = i // 2
            col = i % 2
            
            label = ttk.Label(summary_frame, text=f"{label_text}:")
            label.grid(row=row, column=col*2, sticky=tk.W, padx=5, pady=2)
            
            value_label = ttk.Label(summary_frame, text="$0.00", font=("Arial", 10, "bold"))
            value_label.grid(row=row, column=col*2+1, sticky=tk.W, padx=5, pady=2)
            
            self.summary_labels[key] = value_label
        
        # Refresh button
        refresh_btn = ttk.Button(overview_frame, text="Refresh", command=self.refresh_overview)
        refresh_btn.pack(pady=10)
        
        # Update overview
        self.refresh_overview()
    
    def create_add_record_tab(self):
        """Create add record tab."""
        add_frame = ttk.Frame(self.notebook)
        self.notebook.add(add_frame, text="Add Record")
        
        # Record type selection
        type_frame = ttk.LabelFrame(add_frame, text="Record Type")
        type_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.record_type = tk.StringVar(value="Income")
        types = ["Income", "Expense", "Investment", "Budget"]
        
        for i, record_type in enumerate(types):
            rb = ttk.Radiobutton(type_frame, text=record_type, variable=self.record_type, 
                               value=record_type, command=self.on_record_type_change)
            rb.grid(row=0, column=i, padx=10, pady=5)
        
        # Form frame
        self.form_frame = ttk.LabelFrame(add_frame, text="Record Details")
        self.form_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Create form fields
        self.create_form_fields()
        
        # Add button
        add_btn = ttk.Button(add_frame, text="Add Record", command=self.add_record)
        add_btn.pack(pady=10)
    
    def create_form_fields(self):
        """Create form fields based on record type."""
        # Clear existing widgets
        for widget in self.form_frame.winfo_children():
            widget.destroy()
        
        # Common fields
        self.form_vars = {}
        
        # Amount
        ttk.Label(self.form_frame, text="Amount:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.form_vars['amount'] = tk.StringVar()
        ttk.Entry(self.form_frame, textvariable=self.form_vars['amount']).grid(row=0, column=1, sticky=tk.EW, padx=5, pady=2)
        
        # Description
        ttk.Label(self.form_frame, text="Description:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.form_vars['description'] = tk.StringVar()
        ttk.Entry(self.form_frame, textvariable=self.form_vars['description']).grid(row=1, column=1, sticky=tk.EW, padx=5, pady=2)
        
        # Category
        ttk.Label(self.form_frame, text="Category:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.form_vars['category'] = tk.StringVar()
        ttk.Entry(self.form_frame, textvariable=self.form_vars['category']).grid(row=2, column=1, sticky=tk.EW, padx=5, pady=2)
        
        # Type-specific fields
        record_type = self.record_type.get()
        row = 3
        
        if record_type == "Income":
            ttk.Label(self.form_frame, text="Source:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
            self.form_vars['source'] = tk.StringVar()
            ttk.Entry(self.form_frame, textvariable=self.form_vars['source']).grid(row=row, column=1, sticky=tk.EW, padx=5, pady=2)
            
        elif record_type == "Expense":
            ttk.Label(self.form_frame, text="Vendor:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
            self.form_vars['vendor'] = tk.StringVar()
            ttk.Entry(self.form_frame, textvariable=self.form_vars['vendor']).grid(row=row, column=1, sticky=tk.EW, padx=5, pady=2)
            
        elif record_type == "Investment":
            ttk.Label(self.form_frame, text="Investment Type:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
            self.form_vars['investment_type'] = tk.StringVar()
            ttk.Entry(self.form_frame, textvariable=self.form_vars['investment_type']).grid(row=row, column=1, sticky=tk.EW, padx=5, pady=2)
            
        elif record_type == "Budget":
            ttk.Label(self.form_frame, text="Period:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
            self.form_vars['period'] = tk.StringVar(value="Monthly")
            ttk.Combobox(self.form_frame, textvariable=self.form_vars['period'], 
                        values=["Monthly", "Weekly", "Yearly"]).grid(row=row, column=1, sticky=tk.EW, padx=5, pady=2)
        
        # Configure column weight
        self.form_frame.columnconfigure(1, weight=1)
    
    def create_records_tab(self):
        """Create records viewing tab."""
        records_frame = ttk.Frame(self.notebook)
        self.notebook.add(records_frame, text="View Records")
        
        # Filter frame
        filter_frame = ttk.LabelFrame(records_frame, text="Filter")
        filter_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.filter_type = tk.StringVar(value="All")
        filter_types = ["All", "Income", "Expense", "Investment", "Budget"]
        
        for i, filter_type in enumerate(filter_types):
            rb = ttk.Radiobutton(filter_frame, text=filter_type, variable=self.filter_type, 
                               value=filter_type, command=self.refresh_records_list)
            rb.grid(row=0, column=i, padx=10, pady=5)
        
        # Records list
        list_frame = ttk.LabelFrame(records_frame, text="Records")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Create treeview
        columns = ("Date", "Type", "Description", "Amount", "Category")
        self.records_tree = ttk.Treeview(list_frame, columns=columns, show="headings")
        
        for col in columns:
            self.records_tree.heading(col, text=col)
            self.records_tree.column(col, width=100)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.records_tree.yview)
        self.records_tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack treeview and scrollbar
        self.records_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Refresh records
        self.refresh_records_list()
    
    def create_export_tab(self):
        """Create export tab."""
        export_frame = ttk.Frame(self.notebook)
        self.notebook.add(export_frame, text="Export")
        
        # Export options
        options_frame = ttk.LabelFrame(export_frame, text="Export Options")
        options_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Excel export
        excel_btn = ttk.Button(options_frame, text="Export to Excel", command=self.export_excel)
        excel_btn.pack(pady=5)
        
        # Save data
        save_btn = ttk.Button(options_frame, text="Save Data", command=self.save_data)
        save_btn.pack(pady=5)
        
        # Export log
        log_frame = ttk.LabelFrame(export_frame, text="Export Log")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.export_log = ScrolledText(log_frame, height=10)
        self.export_log.pack(fill=tk.BOTH, expand=True)
    
    def on_record_type_change(self):
        """Handle record type change."""
        self.create_form_fields()
    
    def add_record(self):
        """Add a new record."""
        try:
            # Get form data
            amount = float(self.form_vars['amount'].get())
            description = self.form_vars['description'].get()
            category = self.form_vars['category'].get() or "General"
            
            record_type = self.record_type.get()
            
            # Create record based on type
            if record_type == "Income":
                source = self.form_vars.get('source', tk.StringVar()).get() or "Unknown"
                record = Income(amount=amount, description=description, category=category, source=source)
                
            elif record_type == "Expense":
                vendor = self.form_vars.get('vendor', tk.StringVar()).get() or "Unknown"
                record = Expense(amount=amount, description=description, category=category, vendor=vendor)
                
            elif record_type == "Investment":
                investment_type = self.form_vars.get('investment_type', tk.StringVar()).get() or "Stock"
                record = Investment(amount=amount, description=description, category=category, investment_type=investment_type)
                
            elif record_type == "Budget":
                period = self.form_vars.get('period', tk.StringVar()).get() or "Monthly"
                record = Budget(amount=amount, description=description, category=category, period=period)
            
            # Add to records
            self.records.append(record)
            
            # Clear form
            for var in self.form_vars.values():
                var.set("")
            
            # Refresh displays
            self.refresh_overview()
            self.refresh_records_list()
            
            # Update status
            self.status_var.set(f"Added {record_type}. Total records: {len(self.records)}")
            
            messagebox.showinfo("Success", f"{record_type} record added successfully!")
            
        except ValueError as e:
            messagebox.showerror("Error", f"Invalid input: {e}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to add record: {e}")
    
    def refresh_overview(self):
        """Refresh the overview tab."""
        if not self.records:
            for key, label in self.summary_labels.items():
                label.config(text="$0.00")
            return
        
        analyzer = FinancialAnalyzer(self.records)
        summary = analyzer.get_summary_statistics()
        
        # Update labels
        self.summary_labels['total_income'].config(text=f"${summary['total_income']:,.2f}")
        self.summary_labels['total_expenses'].config(text=f"${summary['total_expenses']:,.2f}")
        self.summary_labels['net_income'].config(text=f"${summary['net_income']:,.2f}")
        self.summary_labels['savings_rate'].config(text=f"{summary['savings_rate']:.1f}%")
    
    def refresh_records_list(self):
        """Refresh the records list."""
        # Clear existing items
        for item in self.records_tree.get_children():
            self.records_tree.delete(item)
        
        # Filter records
        filter_type = self.filter_type.get()
        filtered_records = self.records
        
        if filter_type != "All":
            filtered_records = [r for r in self.records if r.get_type() == filter_type]
        
        # Add records to tree
        for record in filtered_records:
            self.records_tree.insert("", tk.END, values=(
                record.date.strftime('%Y-%m-%d'),
                record.get_type(),
                record.description,
                f"${record.amount:,.2f}",
                record.category
            ))
    
    def export_excel(self):
        """Export data to Excel."""
        try:
            if not self.records:
                messagebox.showwarning("Warning", "No records to export.")
                return
            
            # Get save location
            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
            )
            
            if not filename:
                return
            
            # Export
            exporter = ExcelExporter()
            exporter.export(self.records, filename)
            
            # Log success
            self.export_log.insert(tk.END, f"{datetime.now()}: Exported to {filename}\n")
            self.export_log.see(tk.END)
            
            messagebox.showinfo("Success", f"Data exported to {filename}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Export failed: {e}")
    
    def save_data(self):
        """Save data to JSON file."""
        try:
            # Ensure data directory exists
            self.data_file.parent.mkdir(exist_ok=True)
            
            # Convert records to dictionaries
            data = {
                'records': [record.to_dict() for record in self.records],
                'last_saved': datetime.now().isoformat(),
                'version': '1.0'
            }
            
            with open(self.data_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            self.status_var.set(f"Data saved. Total records: {len(self.records)}")
            messagebox.showinfo("Success", "Data saved successfully!")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save data: {e}")
    
    def load_data(self):
        """Load data from JSON file."""
        try:
            if not self.data_file.exists():
                return
            
            with open(self.data_file, 'r') as f:
                data = json.load(f)
            
            # Reconstruct records from dictionaries
            for record_data in data.get('records', []):
                record = self._dict_to_record(record_data)
                if record:
                    self.records.append(record)
            
            self.logger.info(f"Loaded {len(self.records)} records from {self.data_file}")
            
        except Exception as e:
            self.logger.error(f"Load error: {e}")
    
    def _dict_to_record(self, data):
        """Convert dictionary back to financial record object."""
        try:
            record_type = None
            # Determine record type based on fields
            if 'source' in data:
                record_type = Income
            elif 'vendor' in data:
                record_type = Expense
            elif 'investment_type' in data:
                record_type = Investment
            elif 'period' in data:
                record_type = Budget
            
            if not record_type:
                return None
            
            # Create record with basic fields
            record = record_type(
                amount=data['amount'],
                description=data['description'],
                category=data.get('category', 'General')
            )
            
            # Set additional fields
            for key, value in data.items():
                if hasattr(record, key) and key not in ['amount', 'description', 'category']:
                    if key in ['date', 'created_at', 'updated_at', 'start_date', 'end_date']:
                        if value:
                            setattr(record, key, datetime.fromisoformat(value))
                    else:
                        setattr(record, key, value)
            
            return record
            
        except Exception as e:
            self.logger.error(f"Error converting dict to record: {e}")
            return None
    
    def run(self):
        """Run the GUI application."""
        self.root.mainloop()

def main():
    """Main function to run GUI."""
    if not GUI_AVAILABLE:
        print("GUI not available. Please install tkinter or use the CLI interface.")
        return
    
    app = FinancialGUI()
    app.run()

if __name__ == "__main__":
    main()
