"""
Financial calculation utilities.
"""

from decimal import Decimal, ROUND_HALF_UP
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import statistics

from ..models.financial_records import Income, Expense, Investment, Budget

class FinancialCalculator:
    """Utility class for financial calculations."""
    
    @staticmethod
    def calculate_total(records: List[Any]) -> Decimal:
        """Calculate total amount from a list of financial records."""
        total = Decimal('0')
        for record in records:
            if hasattr(record, 'amount'):
                total += record.amount
        return total.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
    
    @staticmethod
    def calculate_average(records: List[Any]) -> Decimal:
        """Calculate average amount from a list of financial records."""
        if not records:
            return Decimal('0')
        total = FinancialCalculator.calculate_total(records)
        return (total / len(records)).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
    
    @staticmethod
    def calculate_percentage(part: Decimal, whole: Decimal) -> float:
        """Calculate percentage of part relative to whole."""
        if whole == 0:
            return 0.0
        return float((part / whole) * 100)
    
    @staticmethod
    def calculate_growth_rate(old_value: Decimal, new_value: Decimal) -> float:
        """Calculate growth rate between two values."""
        if old_value == 0:
            return 0.0 if new_value == 0 else float('inf')
        return float(((new_value - old_value) / old_value) * 100)
    
    @staticmethod
    def calculate_net_worth(assets: List[Any], liabilities: List[Any]) -> Decimal:
        """Calculate net worth (assets - liabilities)."""
        total_assets = FinancialCalculator.calculate_total(assets)
        total_liabilities = FinancialCalculator.calculate_total(liabilities)
        return total_assets - total_liabilities
    
    @staticmethod
    def calculate_savings_rate(income: Decimal, expenses: Decimal) -> float:
        """Calculate savings rate as percentage of income."""
        if income == 0:
            return 0.0
        savings = income - expenses
        return float((savings / income) * 100)
    
    @staticmethod
    def calculate_expense_ratio(category_expenses: Decimal, total_expenses: Decimal) -> float:
        """Calculate expense ratio for a specific category."""
        return FinancialCalculator.calculate_percentage(category_expenses, total_expenses)
    
    @staticmethod
    def calculate_monthly_average(records: List[Any], months: int = 12) -> Decimal:
        """Calculate monthly average over specified period."""
        if not records or months <= 0:
            return Decimal('0')
        total = FinancialCalculator.calculate_total(records)
        return (total / months).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
    
    @staticmethod
    def calculate_compound_interest(principal: Decimal, rate: float, time: float, 
                                 compound_frequency: int = 12) -> Decimal:
        """
        Calculate compound interest.
        
        Args:
            principal: Initial amount
            rate: Annual interest rate (as decimal, e.g., 0.05 for 5%)
            time: Time in years
            compound_frequency: Compounding frequency per year
        """
        if rate <= 0 or time <= 0:
            return principal
        
        amount = principal * ((1 + rate / compound_frequency) ** (compound_frequency * time))
        return Decimal(str(amount)).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
    
    @staticmethod
    def calculate_roi(initial_investment: Decimal, current_value: Decimal) -> float:
        """Calculate Return on Investment (ROI)."""
        if initial_investment == 0:
            return 0.0
        return float(((current_value - initial_investment) / initial_investment) * 100)

class FinancialAnalyzer:
    """Advanced financial analysis utilities."""
    
    def __init__(self, records: List[Any]):
        """Initialize with financial records."""
        self.records = records
        self.incomes = [r for r in records if isinstance(r, Income)]
        self.expenses = [r for r in records if isinstance(r, Expense)]
        self.investments = [r for r in records if isinstance(r, Investment)]
        self.budgets = [r for r in records if isinstance(r, Budget)]
    
    def get_summary_statistics(self) -> Dict[str, Any]:
        """Get comprehensive financial summary."""
        total_income = FinancialCalculator.calculate_total(self.incomes)
        total_expenses = FinancialCalculator.calculate_total(self.expenses)
        total_investments = FinancialCalculator.calculate_total(self.investments)
        
        return {
            'total_income': float(total_income),
            'total_expenses': float(total_expenses),
            'total_investments': float(total_investments),
            'net_income': float(total_income - total_expenses),
            'savings_rate': FinancialCalculator.calculate_savings_rate(total_income, total_expenses),
            'average_income': float(FinancialCalculator.calculate_average(self.incomes)),
            'average_expense': float(FinancialCalculator.calculate_average(self.expenses)),
            'record_count': {
                'income': len(self.incomes),
                'expenses': len(self.expenses),
                'investments': len(self.investments),
                'budgets': len(self.budgets)
            }
        }
    
    def get_category_breakdown(self, record_type: str = 'expense') -> Dict[str, Dict[str, Any]]:
        """Get breakdown by category."""
        if record_type == 'expense':
            records = self.expenses
        elif record_type == 'income':
            records = self.incomes
        else:
            records = []
        
        categories = {}
        total = FinancialCalculator.calculate_total(records)
        
        for record in records:
            category = record.category
            if category not in categories:
                categories[category] = {
                    'total': Decimal('0'),
                    'count': 0,
                    'percentage': 0.0,
                    'average': Decimal('0')
                }
            
            categories[category]['total'] += record.amount
            categories[category]['count'] += 1
        
        # Calculate percentages and averages
        for category, data in categories.items():
            data['percentage'] = FinancialCalculator.calculate_percentage(data['total'], total)
            data['average'] = data['total'] / data['count'] if data['count'] > 0 else Decimal('0')
            data['total'] = float(data['total'])
            data['average'] = float(data['average'])
        
        return categories
    
    def get_budget_analysis(self) -> Dict[str, Any]:
        """Analyze budget performance."""
        if not self.budgets:
            return {}
        
        analysis = {
            'total_budgeted': 0.0,
            'total_spent': 0.0,
            'over_budget_count': 0,
            'average_utilization': 0.0,
            'budget_details': []
        }
        
        total_budgeted = Decimal('0')
        total_spent = Decimal('0')
        utilizations = []
        
        for budget in self.budgets:
            total_budgeted += budget.amount
            total_spent += budget.spent
            utilization = budget.get_utilization_percentage()
            utilizations.append(utilization)
            
            if budget.is_over_budget():
                analysis['over_budget_count'] += 1
            
            analysis['budget_details'].append({
                'description': budget.description,
                'budgeted': float(budget.amount),
                'spent': float(budget.spent),
                'remaining': float(budget.get_remaining()),
                'utilization': utilization,
                'over_budget': budget.is_over_budget()
            })
        
        analysis['total_budgeted'] = float(total_budgeted)
        analysis['total_spent'] = float(total_spent)
        analysis['average_utilization'] = statistics.mean(utilizations) if utilizations else 0.0
        
        return analysis
