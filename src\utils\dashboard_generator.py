"""
Dashboard and report generation utilities.
"""

from datetime import datetime, timed<PERSON>ta
from typing import List, Any, Dict, Tuple
from decimal import Decimal
import json
from pathlib import Path

from ..models.financial_records import Income, Expense, Investment, Budget
from ..utils.calculations import FinancialAnalyzer, FinancialCalculator
from ..utils.logger import setup_logger

class DashboardGenerator:
    """Generate financial dashboards and reports."""
    
    def __init__(self, records: List[Any]):
        """Initialize with financial records."""
        self.records = records
        self.analyzer = FinancialAnalyzer(records)
        self.logger = setup_logger()
    
    def generate_monthly_report(self, year: int, month: int) -> Dict[str, Any]:
        """
        Generate a monthly financial report.
        
        Args:
            year: Year for the report
            month: Month for the report (1-12)
            
        Returns:
            Dict containing monthly report data
        """
        # Filter records for the specified month
        monthly_records = self._filter_records_by_month(year, month)
        
        if not monthly_records:
            return {
                'period': f"{year}-{month:02d}",
                'message': 'No records found for this period'
            }
        
        monthly_analyzer = FinancialAnalyzer(monthly_records)
        summary = monthly_analyzer.get_summary_statistics()
        
        report = {
            'period': f"{year}-{month:02d}",
            'summary': summary,
            'category_breakdown': {
                'income': monthly_analyzer.get_category_breakdown('income'),
                'expense': monthly_analyzer.get_category_breakdown('expense')
            },
            'budget_analysis': monthly_analyzer.get_budget_analysis(),
            'trends': self._calculate_monthly_trends(year, month),
            'recommendations': self._generate_recommendations(summary)
        }
        
        return report
    
    def generate_yearly_report(self, year: int) -> Dict[str, Any]:
        """
        Generate a yearly financial report.
        
        Args:
            year: Year for the report
            
        Returns:
            Dict containing yearly report data
        """
        # Filter records for the specified year
        yearly_records = self._filter_records_by_year(year)
        
        if not yearly_records:
            return {
                'period': str(year),
                'message': 'No records found for this year'
            }
        
        yearly_analyzer = FinancialAnalyzer(yearly_records)
        summary = yearly_analyzer.get_summary_statistics()
        
        # Generate monthly breakdown
        monthly_breakdown = {}
        for month in range(1, 13):
            month_records = self._filter_records_by_month(year, month)
            if month_records:
                month_analyzer = FinancialAnalyzer(month_records)
                monthly_breakdown[f"{month:02d}"] = month_analyzer.get_summary_statistics()
        
        report = {
            'period': str(year),
            'summary': summary,
            'monthly_breakdown': monthly_breakdown,
            'category_breakdown': {
                'income': yearly_analyzer.get_category_breakdown('income'),
                'expense': yearly_analyzer.get_category_breakdown('expense')
            },
            'budget_analysis': yearly_analyzer.get_budget_analysis(),
            'yearly_trends': self._calculate_yearly_trends(year),
            'goals_analysis': self._analyze_financial_goals(yearly_records),
            'recommendations': self._generate_recommendations(summary)
        }
        
        return report
    
    def generate_dashboard_data(self) -> Dict[str, Any]:
        """
        Generate comprehensive dashboard data.
        
        Returns:
            Dict containing dashboard data
        """
        summary = self.analyzer.get_summary_statistics()
        
        dashboard = {
            'overview': summary,
            'recent_transactions': self._get_recent_transactions(10),
            'category_breakdown': {
                'income': self.analyzer.get_category_breakdown('income'),
                'expense': self.analyzer.get_category_breakdown('expense')
            },
            'budget_status': self.analyzer.get_budget_analysis(),
            'trends': self._calculate_recent_trends(),
            'alerts': self._generate_alerts(),
            'quick_stats': self._generate_quick_stats(),
            'generated_at': datetime.now().isoformat()
        }
        
        return dashboard
    
    def export_dashboard_html(self, filepath: str) -> bool:
        """
        Export dashboard as HTML file.
        
        Args:
            filepath: Output file path
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            dashboard_data = self.generate_dashboard_data()
            html_content = self._generate_html_dashboard(dashboard_data)
            
            Path(filepath).parent.mkdir(parents=True, exist_ok=True)
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            self.logger.info(f"Dashboard exported to HTML: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error exporting HTML dashboard: {e}")
            return False
    
    def _filter_records_by_month(self, year: int, month: int) -> List[Any]:
        """Filter records by specific month and year."""
        filtered = []
        for record in self.records:
            if record.date.year == year and record.date.month == month:
                filtered.append(record)
        return filtered
    
    def _filter_records_by_year(self, year: int) -> List[Any]:
        """Filter records by specific year."""
        filtered = []
        for record in self.records:
            if record.date.year == year:
                filtered.append(record)
        return filtered
    
    def _calculate_monthly_trends(self, year: int, month: int) -> Dict[str, Any]:
        """Calculate trends compared to previous month."""
        current_records = self._filter_records_by_month(year, month)
        
        # Get previous month
        if month == 1:
            prev_year, prev_month = year - 1, 12
        else:
            prev_year, prev_month = year, month - 1
        
        prev_records = self._filter_records_by_month(prev_year, prev_month)
        
        if not prev_records:
            return {'message': 'No previous month data for comparison'}
        
        current_analyzer = FinancialAnalyzer(current_records)
        prev_analyzer = FinancialAnalyzer(prev_records)
        
        current_summary = current_analyzer.get_summary_statistics()
        prev_summary = prev_analyzer.get_summary_statistics()
        
        trends = {}
        for key in ['total_income', 'total_expenses', 'net_income']:
            current_val = Decimal(str(current_summary[key]))
            prev_val = Decimal(str(prev_summary[key]))
            
            if prev_val != 0:
                change_pct = float(((current_val - prev_val) / prev_val) * 100)
            else:
                change_pct = 0.0
            
            trends[key] = {
                'current': float(current_val),
                'previous': float(prev_val),
                'change_amount': float(current_val - prev_val),
                'change_percentage': change_pct
            }
        
        return trends
    
    def _calculate_yearly_trends(self, year: int) -> Dict[str, Any]:
        """Calculate trends compared to previous year."""
        current_records = self._filter_records_by_year(year)
        prev_records = self._filter_records_by_year(year - 1)
        
        if not prev_records:
            return {'message': 'No previous year data for comparison'}
        
        current_analyzer = FinancialAnalyzer(current_records)
        prev_analyzer = FinancialAnalyzer(prev_records)
        
        current_summary = current_analyzer.get_summary_statistics()
        prev_summary = prev_analyzer.get_summary_statistics()
        
        trends = {}
        for key in ['total_income', 'total_expenses', 'total_investments', 'net_income']:
            current_val = Decimal(str(current_summary[key]))
            prev_val = Decimal(str(prev_summary[key]))
            
            if prev_val != 0:
                change_pct = float(((current_val - prev_val) / prev_val) * 100)
            else:
                change_pct = 0.0
            
            trends[key] = {
                'current': float(current_val),
                'previous': float(prev_val),
                'change_amount': float(current_val - prev_val),
                'change_percentage': change_pct
            }
        
        return trends
    
    def _calculate_recent_trends(self) -> Dict[str, Any]:
        """Calculate trends for recent periods."""
        now = datetime.now()
        
        # Last 30 days vs previous 30 days
        last_30_days = [r for r in self.records if (now - r.date).days <= 30]
        prev_30_days = [r for r in self.records if 30 < (now - r.date).days <= 60]
        
        if not prev_30_days:
            return {'message': 'Insufficient data for trend analysis'}
        
        recent_analyzer = FinancialAnalyzer(last_30_days)
        prev_analyzer = FinancialAnalyzer(prev_30_days)
        
        recent_summary = recent_analyzer.get_summary_statistics()
        prev_summary = prev_analyzer.get_summary_statistics()
        
        trends = {}
        for key in ['total_income', 'total_expenses', 'net_income']:
            recent_val = Decimal(str(recent_summary[key]))
            prev_val = Decimal(str(prev_summary[key]))
            
            if prev_val != 0:
                change_pct = float(((recent_val - prev_val) / prev_val) * 100)
            else:
                change_pct = 0.0
            
            trends[key] = {
                'recent_30_days': float(recent_val),
                'previous_30_days': float(prev_val),
                'change_percentage': change_pct
            }
        
        return trends
    
    def _get_recent_transactions(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get most recent transactions."""
        sorted_records = sorted(self.records, key=lambda x: x.date, reverse=True)
        recent = sorted_records[:limit]
        
        transactions = []
        for record in recent:
            transactions.append({
                'date': record.date.strftime('%Y-%m-%d'),
                'type': record.get_type(),
                'description': record.description,
                'amount': float(record.amount),
                'category': record.category
            })
        
        return transactions
    
    def _generate_alerts(self) -> List[Dict[str, str]]:
        """Generate financial alerts and warnings."""
        alerts = []
        
        # Budget alerts
        budgets = [r for r in self.records if isinstance(r, Budget)]
        for budget in budgets:
            if budget.is_over_budget():
                alerts.append({
                    'type': 'warning',
                    'message': f"Budget '{budget.description}' is over by ${float(budget.spent - budget.amount):,.2f}"
                })
            elif budget.get_utilization_percentage() > 90:
                alerts.append({
                    'type': 'info',
                    'message': f"Budget '{budget.description}' is {budget.get_utilization_percentage():.1f}% utilized"
                })
        
        # Expense pattern alerts
        summary = self.analyzer.get_summary_statistics()
        if summary['savings_rate'] < 10:
            alerts.append({
                'type': 'warning',
                'message': f"Low savings rate: {summary['savings_rate']:.1f}%. Consider reducing expenses."
            })
        
        return alerts
    
    def _generate_quick_stats(self) -> Dict[str, Any]:
        """Generate quick statistics for dashboard."""
        summary = self.analyzer.get_summary_statistics()
        
        # Calculate additional metrics
        expenses = [r for r in self.records if isinstance(r, Expense)]
        essential_expenses = [e for e in expenses if e.is_essential]
        non_essential_expenses = [e for e in expenses if not e.is_essential]
        
        essential_total = FinancialCalculator.calculate_total(essential_expenses)
        non_essential_total = FinancialCalculator.calculate_total(non_essential_expenses)
        
        return {
            'total_records': len(self.records),
            'essential_expenses': float(essential_total),
            'non_essential_expenses': float(non_essential_total),
            'average_transaction': float(FinancialCalculator.calculate_average(self.records)),
            'largest_expense': float(max([e.amount for e in expenses], default=0)),
            'largest_income': float(max([i.amount for i in [r for r in self.records if isinstance(r, Income)]], default=0))
        }

    def _analyze_financial_goals(self, records: List[Any]) -> Dict[str, Any]:
        """Analyze progress towards financial goals."""
        analyzer = FinancialAnalyzer(records)
        summary = analyzer.get_summary_statistics()

        # Define some common financial goals and benchmarks
        goals_analysis = {
            'emergency_fund': {
                'target_months': 6,  # 6 months of expenses
                'current_savings': summary['net_income'],
                'monthly_expenses': summary['total_expenses'] / 12 if summary['total_expenses'] > 0 else 0,
                'target_amount': (summary['total_expenses'] / 12) * 6 if summary['total_expenses'] > 0 else 0,
                'progress_percentage': 0
            },
            'savings_rate': {
                'target_percentage': 20,  # 20% savings rate
                'current_percentage': summary['savings_rate'],
                'status': 'on_track' if summary['savings_rate'] >= 20 else 'needs_improvement'
            },
            'investment_allocation': {
                'target_percentage': 15,  # 15% of income to investments
                'current_amount': summary['total_investments'],
                'current_percentage': (summary['total_investments'] / summary['total_income'] * 100) if summary['total_income'] > 0 else 0,
                'status': 'on_track' if (summary['total_investments'] / summary['total_income'] * 100) >= 15 else 'needs_improvement'
            }
        }

        # Calculate emergency fund progress
        if goals_analysis['emergency_fund']['target_amount'] > 0:
            goals_analysis['emergency_fund']['progress_percentage'] = min(
                (goals_analysis['emergency_fund']['current_savings'] / goals_analysis['emergency_fund']['target_amount']) * 100,
                100
            )

        return goals_analysis

    def _generate_recommendations(self, summary: Dict[str, Any]) -> List[str]:
        """Generate financial recommendations based on analysis."""
        recommendations = []

        # Savings rate recommendations
        if summary['savings_rate'] < 10:
            recommendations.append("Consider reducing non-essential expenses to improve your savings rate.")
        elif summary['savings_rate'] < 20:
            recommendations.append("Good savings rate! Try to reach 20% for optimal financial health.")
        else:
            recommendations.append("Excellent savings rate! Consider increasing investments.")

        # Income vs expenses
        if summary['net_income'] < 0:
            recommendations.append("Your expenses exceed income. Review and cut unnecessary expenses immediately.")

        # Investment recommendations
        investment_rate = (summary['total_investments'] / summary['total_income'] * 100) if summary['total_income'] > 0 else 0
        if investment_rate < 10:
            recommendations.append("Consider increasing your investment allocation to build long-term wealth.")

        return recommendations

    def _generate_html_dashboard(self, dashboard_data: Dict[str, Any]) -> str:
        """Generate HTML content for dashboard."""
        html_template = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financial Dashboard</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #366092;
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .stat-card {{
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #366092;
        }}
        .stat-value {{
            font-size: 24px;
            font-weight: bold;
            color: #366092;
        }}
        .stat-label {{
            color: #666;
            margin-top: 5px;
        }}
        .section {{
            margin-bottom: 30px;
        }}
        .section-title {{
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }}
        .alert {{
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }}
        .alert-warning {{
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }}
        .alert-info {{
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }}
        .transaction {{
            display: flex;
            justify-content: space-between;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }}
        .transaction:last-child {{
            border-bottom: none;
        }}
        .amount-positive {{
            color: #28a745;
        }}
        .amount-negative {{
            color: #dc3545;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Financial Dashboard</h1>
            <p>Generated on: {dashboard_data['generated_at'][:19].replace('T', ' ')}</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">${dashboard_data['overview']['total_income']:,.2f}</div>
                <div class="stat-label">Total Income</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">${dashboard_data['overview']['total_expenses']:,.2f}</div>
                <div class="stat-label">Total Expenses</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">${dashboard_data['overview']['net_income']:,.2f}</div>
                <div class="stat-label">Net Income</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{dashboard_data['overview']['savings_rate']:.1f}%</div>
                <div class="stat-label">Savings Rate</div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">Alerts</div>
            {self._generate_alerts_html(dashboard_data['alerts'])}
        </div>

        <div class="section">
            <div class="section-title">Recent Transactions</div>
            {self._generate_transactions_html(dashboard_data['recent_transactions'])}
        </div>
    </div>
</body>
</html>
        """
        return html_template

    def _generate_alerts_html(self, alerts: List[Dict[str, str]]) -> str:
        """Generate HTML for alerts section."""
        if not alerts:
            return '<p>No alerts at this time.</p>'

        html = ''
        for alert in alerts:
            alert_class = f"alert-{alert['type']}"
            html += f'<div class="alert {alert_class}">{alert["message"]}</div>'

        return html

    def _generate_transactions_html(self, transactions: List[Dict[str, Any]]) -> str:
        """Generate HTML for transactions section."""
        if not transactions:
            return '<p>No recent transactions.</p>'

        html = ''
        for transaction in transactions:
            amount_class = "amount-positive" if transaction['type'] == 'Income' else "amount-negative"
            sign = "+" if transaction['type'] == 'Income' else "-"
            html += f'''
            <div class="transaction">
                <div>
                    <strong>{transaction['description']}</strong><br>
                    <small>{transaction['date']} • {transaction['category']}</small>
                </div>
                <div class="{amount_class}">
                    {sign}${transaction['amount']:,.2f}
                </div>
            </div>
            '''

        return html
